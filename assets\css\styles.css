:root {
  --primary-color: #2196F3;
  --secondary-color: #0D47A1;
  --text-color: #333;
  --light-gray: #f5f5f5;
  --border-color: #ddd;
  --success-color: #4CAF50;
  --error-color: #F44336;

  /* 动画变量 */
  --animation-duration: 0.3s;
  --animation-duration-slow: 0.6s;
  --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  /* 可选择的渐变背景色彩搭配 */
  /* 选项1: 清新蓝绿 */
  /* background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); */

  /* 选项2: 温暖橙粉 */
  /* background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%); */

  /* 选项3: 深邃紫蓝 */
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);

  /* 选项4: 清雅绿青 */
  /* background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); */

  /* 选项5: 经典灰蓝 */
  /* background: linear-gradient(135deg, #636e72 0%, #b2bec3 100%); */
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  max-width: 600px;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp var(--animation-duration-slow) var(--animation-easing) forwards;
}

.search-container {
  display: flex;
  margin-bottom: 20px;
  background-color: white;
  padding: 15px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(-20px);
  animation: slideInDown var(--animation-duration-slow) var(--animation-easing) 0.2s forwards;
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 16px;
  outline: none;
  transition: all var(--animation-duration) var(--animation-easing);
  background-color: #fafafa;
}

.search-input:focus {
  border-color: var(--primary-color);
  background-color: white;
  transform: scale(1.02);
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.search-button {
  margin-left: 12px;
  padding: 12px 24px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--animation-duration) var(--animation-easing);
  position: relative;
  overflow: hidden;
}

.search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(33, 150, 243, 0.3);
}

.search-button:active {
  transform: translateY(0);
  animation: pulse 0.3s ease;
}

.results-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
  animation: slideInUp var(--animation-duration-slow) var(--animation-easing) 0.4s forwards;
}

.result-item {
  padding: 18px;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  transition: all var(--animation-duration) var(--animation-easing);
  opacity: 0;
  transform: translateX(-20px);
  animation: slideInUp var(--animation-duration) var(--animation-easing) forwards;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item:nth-child(even) {
  background-color: rgba(245, 245, 245, 0.5);
}

.result-item:hover {
  background-color: rgba(33, 150, 243, 0.05);
  transform: translateX(5px);
}

.copy-button {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 6px 12px;
  background-color: transparent;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: pointer;
  transition: all var(--animation-duration) var(--animation-easing-bounce);
  font-size: 12px;
  opacity: 0.7;
}

.copy-button:hover {
  background-color: var(--primary-color);
  color: white;
  opacity: 1;
  transform: scale(1.1);
}

.copy-button.copied {
  background-color: var(--success-color);
  color: white;
  transform: scale(1.2);
  animation: pulse 0.5s ease;
}

.loading {
  text-align: center;
  padding: 20px;
}

.error-message {
  color: var(--error-color);
  padding: 15px;
  text-align: center;
}

/* 动画关键帧 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@media (max-width: 600px) {
  body {
    padding: 10px;
  }

  .container {
    padding: 20px;
  }

  .search-container {
    flex-direction: column;
  }

  .search-button {
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
  }
}